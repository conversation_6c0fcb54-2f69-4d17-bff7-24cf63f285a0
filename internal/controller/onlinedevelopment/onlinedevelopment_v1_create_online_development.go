package onlinedevelopment

import (
	"context"
	"mlops/internal/consts"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) CreateOnlineDevelopment(ctx context.Context, req *v1.CreateOnlineDevelopmentReq) (res *v1.CreateOnlineDevelopmentRes, err error) {
	res = &v1.CreateOnlineDevelopmentRes{}
	onlineDev := &dto.OnlineDevelopment{
		DevName:             req.DevName,
		TeamId:              req.TeamId,
		TeamName:            req.TeamName,
		ClusterName:         req.ClusterName,
		ClusterId:           req.ClusterId,
		Namespace:           req.Namespace,
		ImageUrl:            req.ImageUrl,
		Type:                req.Type,
		CreatedByEmployeeNo: req.CreatedByEmployeeNo,
		CreatedByUserName:   req.CreatedByUserName,
		UpdatedByEmployeeNo: req.UpdatedByEmployeeNo,
		UpdatedByUserName:   req.UpdatedByUserName,
		ClusterResource:     req.ClusterResource,
		VolumeMounts:        req.VolumeMounts,
		Env:                 req.Env,
		Status:              consts.OnlineDevelopmentStatusShutdown,
	}
	err = service.OnlineDevelopment().Create(ctx, onlineDev)
	if err != nil {
		return nil, err
	}
	return
}
