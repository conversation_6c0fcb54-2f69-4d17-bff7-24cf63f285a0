package onlinedevelopment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
	"mlops/internal/service"
	"strconv"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) UpdateOnlineDevelopment(ctx context.Context, req *v1.UpdateOnlineDevelopmentReq) (res *v1.UpdateOnlineDevelopmentRes, err error) {
	res = &v1.UpdateOnlineDevelopmentRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	onlineDev := &dto.OnlineDevelopment{
		Id:                  uint(idInt),
		DevName:             req.DevName,
		ClusterName:         req.ClusterName,
		ClusterId:           req.ClusterId,
		Namespace:           req.Namespace,
		ImageUrl:            req.ImageUrl,
		Type:                req.Type,
		CreatedByEmployeeNo: req.CreatedByEmployeeNo,
		CreatedByUserName:   req.CreatedByUserName,
		UpdatedByEmployeeNo: req.UpdatedByEmployeeNo,
		UpdatedByUserName:   req.UpdatedByUserName,
		ClusterResource:     req.ClusterResource,
		VolumeMounts:        req.VolumeMounts,
		Env:                 req.Env,
	}
	err = service.OnlineDevelopment().Update(ctx, onlineDev)
	if err != nil {
		return nil, err
	}
	return
}
