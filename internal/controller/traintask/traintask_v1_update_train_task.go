package traintask

import (
	"context"
	"strconv"

	v1 "mlops/api/traintask/v1"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) UpdateTrainTask(ctx context.Context, req *v1.UpdateTrainTaskReq) (res *v1.UpdateTrainTaskRes, err error) {
	res = &v1.UpdateTrainTaskRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}
	trainTask := &dto.TrainTask{
		Id:                  uint(idInt),
		TaskName:            req.TaskName,
		ClusterName:         req.ClusterName,
		ClusterId:           req.ClusterId,
		Namespace:           req.Namespace,
		ImageUrl:            req.ImageUrl,
		StartCmd:            req.StartCmd,
		Priority:            req.Priority,
		TaskType:            req.TaskType,
		TaskYaml:            req.<PERSON>,
		CreatedByEmployeeNo: req.CreatedByEmployeeNo,
		CreatedByUserName:   req.CreatedByUserName,
		UpdatedByEmployeeNo: req.UpdatedByEmployeeNo,
		UpdatedByUserName:   req.UpdatedByUserName,
		ClusterResource:     req.ClusterResource,
		VolumeMounts:        req.VolumeMounts,
		EnvVarsMap:          req.EnvVarsMap,
	}
	err = service.TrainTask().Update(ctx, trainTask)
	if err != nil {
		return nil, err
	}
	return
}
