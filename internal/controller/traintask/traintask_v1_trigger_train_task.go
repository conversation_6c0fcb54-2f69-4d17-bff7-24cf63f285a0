package traintask

import (
	"context"
	"errors"
	"strconv"

	v1 "mlops/api/traintask/v1"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) TriggerTrainTask(ctx context.Context, req *v1.TriggerTrainTaskReq) (res *v1.TriggerTrainTaskRes, err error) {
	res = &v1.TriggerTrainTaskRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}

	_, err = service.TrainTask().Get(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}

	if req.TriggerSource == "" {
		return nil, errors.New("triggerSource is required")
	}
	if req.TriggerSource != "MANUAL" && req.TriggerSource != "SCHEDULED" && req.TriggerSource != "API_CALL" {
		return nil, errors.New("triggerSource is invalid")
	}
	if req.TriggeredByUserName == "" || req.TriggeredByEmployeeNo == "" {
		user := service.BizCtx().Get(ctx).User
		req.TriggeredByUserName = user.NickName
		req.TriggeredByEmployeeNo = user.EmployeeNo
	}
	if req.TriggeredByUserName == "" {
		return nil, errors.New("triggeredByUserName is required")
	}
	if req.TriggeredByEmployeeNo == "" {
		return nil, errors.New("triggeredByEmployeeNo is required")
	}

	err = service.TrainTask().Trigger(ctx, uint(idInt), req.TriggerSource, req.TriggeredByUserName, req.TriggeredByEmployeeNo)
	if err != nil {
		return nil, err
	}
	return
}
