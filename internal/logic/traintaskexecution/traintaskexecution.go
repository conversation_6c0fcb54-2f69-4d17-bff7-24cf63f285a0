package traintaskexecution

import (
	"context"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"strings"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
)

type sTrainTaskExecution struct {
}

func init() {
	service.RegisterTrainTaskExecution(newsTrainTaskExecution())
}

func newsTrainTaskExecution() *sTrainTaskExecution {
	return &sTrainTaskExecution{}
}

func (this *sTrainTaskExecution) Table() string {
	return dao.TrainTaskExecution.Table()
}

func (this *sTrainTaskExecution) ListPage(ctx context.Context, in dto.TrainTaskExecutionListInput) (pageData *dto.TrainTaskExecutionListOutput, err error) {
	list := make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx).Unscoped()
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if in.ExecutionId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().Id, in.ExecutionId)
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TeamId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TeamId, in.TeamId)
	}
	if in.TriggeredByUserName != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByUserName, in.TriggeredByUserName)
	}
	if in.TriggeredByEmployeeNo != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByEmployeeNo, in.TriggeredByEmployeeNo)
	}
	if len(in.TriggerSources) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSources)
	}
	if len(in.TriggerTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().TriggerTime, in.TriggerTime[0], in.TriggerTime[1])
	}
	if len(in.StartTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().StartTime, in.StartTime[0], in.StartTime[1])
	}
	if len(in.EndTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().EndTime, in.EndTime[0], in.EndTime[1])
	}
	q = q.LeftJoin(dao.TrainTask.Table(), "tt_train_task_execution.task_id = tt_train_task.id")
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTask.Columns().TaskName, "%"+in.TaskName+"%")
	}
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.Fields("tt_train_task_execution.*", "tt_train_task.task_name").OrderDesc(dao.TrainTaskExecution.Columns().Id).Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}

	pageData = &dto.TrainTaskExecutionListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}
	return
}

func (this *sTrainTaskExecution) List(ctx context.Context, in dto.TrainTaskExecutionListInput) (list []*dto.TrainTaskExecution, err error) {
	list = make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx)
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TriggeredByUserName != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByUserName, in.TriggeredByUserName)
	}
	if len(in.TriggerSources) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSources)
	}
	if len(in.TriggerTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().TriggerTime, in.TriggerTime[0], in.TriggerTime[1])
	}
	if len(in.StartTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().StartTime, in.StartTime[0], in.StartTime[1])
	}
	if len(in.EndTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().EndTime, in.EndTime[0], in.EndTime[1])
	}
	err = q.OrderDesc(dao.TrainTaskExecution.Columns().Id).Scan(&list)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Get(ctx context.Context, id uint) (trainTaskExecution *dto.TrainTaskExecution, err error) {
	trainTaskExecution = &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Interrupt(ctx context.Context, id uint) (err error) {
	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return err
	}
	if trainTaskExecution.Status != "RUNNING" && trainTaskExecution.Status != "PENDING" {
		return nil
	}
	err = dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// delete rayjob
		err = client.DeleteRayJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
		if err != nil {
			return err
		}
		// update status
		trainTaskExecution.Status = "CANCELLED"
		trainTaskExecution.EndTime = gtime.Now()
		trainTaskExecution.Duration = uint(trainTaskExecution.EndTime.Sub(trainTaskExecution.TriggerTime).Minutes())
		_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().Id, id).Data(trainTaskExecution).Update()
		if err != nil {
			return err
		}

		// update complete_count && last_status
		_, err = tx.Model(dao.TrainTask.Table()).Data(g.Map{"complete_count": gdb.Raw("complete_count + 1"), "last_status": trainTaskExecution.Status}).Where(dao.TrainTask.Columns().Id, trainTaskExecution.TaskId).Update()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTaskExecution) SyncTaskExecutionStatus(ctx context.Context) (err error) {
	allTriggeredExecutions := make([]*dto.TrainTaskExecution, 0)
	err = dao.TrainTaskExecution.Ctx(ctx).WhereIn(dao.TrainTaskExecution.Columns().Status, []string{"RUNNING", "PENDING"}).Scan(&allTriggeredExecutions)
	if err != nil {
		return err
	}
	for _, execution := range allTriggeredExecutions {
		// get rayjob status
		rayjob, err := client.GetRayJob(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
		status := ""
		if err != nil {
			log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get rayjob error:%s", err.Error())
			if strings.Contains(err.Error(), "not found") {
				status = "CANCELLED"
			} else {
				continue
			}
		} else {
			status = string(rayjob.Status.JobStatus)
		}
		if status == "" {
			log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "can not get rayjob status")
			continue
		}
		dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			trainTask := &entity.TrainTask{}
			err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, execution.TaskId).Scan(trainTask)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get train task error:%s", err.Error())
				return err
			}
			trainTask.LastStatus = status
			_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, execution.TaskId).Data(trainTask).Update()
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update train task error:%s", err.Error())
				return err
			}
			execution.Status = status
			if status == "SUCCEEDED" || status == "FAILED" || status == "STOPPED" || status == "CANCELLED" {
				execution.EndTime = gtime.Now()
				execution.Duration = uint(execution.EndTime.Sub(execution.TriggerTime).Minutes())
				// 更新完成计数
				_, err = tx.Model(dao.TrainTask.Table()).Data("complete_count", gdb.Raw("complete_count + 1")).Where(dao.TrainTask.Columns().Id, execution.TaskId).Update()
				if err != nil {
					log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update train task error:%s", err.Error())
					return err
				}
			}
			_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().Id, execution.Id).Data(execution).Update()
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update rayjob error:%s", err.Error())
				return err
			}
			return nil
		})
	}
	return
}
