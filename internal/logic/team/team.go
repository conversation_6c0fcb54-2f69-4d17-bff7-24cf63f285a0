package team

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gdb"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cmdb/api"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/slice"
	"mlops/internal/consts"
	"mlops/internal/consts/auth"
	"mlops/internal/dao"
	"mlops/internal/model/do"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"strings"
)

/**
team:
定时向cicd拉取团队列表 并持久化在team表中
*/

type sTeam struct {
	teamSynchronizer *teamSynchronizer
}

func init() {
	service.RegisterTeam(newsTeam())
}

func newsTeam() *sTeam {
	return &sTeam{
		teamSynchronizer: newTeamSynchronizer(),
	}
}

func (this *sTeam) Table() string {
	return dao.Team.Table()
}

func (this *sTeam) Sync(ctx context.Context) (err error) {
	return this.teamSynchronizer.start(ctx)
}

// SyncTeamOrganization 根据用户id同步组织架构团队
func (this *sTeam) SyncTeamOrganization(ctx context.Context, userId int) (pid int64, err error) {
	// 同步当前团队
	userInfo, err := service.User().GetUserById(ctx, userId)
	if err != nil {
		return 0, err
	}

	relations, err := client.CmdbApiJsonRpc.GetUserOrganizationRelation(ctx, userInfo.EmployeeNo)
	if err != nil {
		return 0, err
	}

	slice.QuickSort[*api.OrganizationRelationInfo](relations, func(a, b *api.OrganizationRelationInfo) bool {
		return a.Level < b.Level
	})

	var (
		teamName string
	)

	for _, relation := range relations {
		teamName = fmt.Sprintf("%s-%s", teamName, relation.DeptName)
	}

	teamId, err := gmd5.Encrypt(teamName)
	if err != nil {
		return 0, err
	}

	teamName = strings.Trim(teamName, "-")

	res, err := dao.Team.Ctx(ctx).Data(do.Team{
		Category: auth.TeamOrganization,
		Name:     teamName,
		TeamId:   teamId,
	}).Save()
	if err != nil {
		return 0, err
	}

	return res.LastInsertId()
}

func (this *sTeam) List(ctx context.Context) (list []*entity.Team, err error) {
	err = dao.Team.Ctx(ctx).Scan(&list)
	return
}

func (this *sTeam) ListRelatedClusterNamespace(ctx context.Context, teamId int) (res []*dto.ClusterNamespace, err error) {
	res = make([]*dto.ClusterNamespace, 0)
	val, err := service.Setting().GetVal(ctx, consts.GenerateTeamRelatedClusterNamespaceKey(teamId))
	if err != nil {
		return nil, err
	}

	log.L.WithName("sTeam.ListRelatedClusterNamespace").Infof(ctx, "team related cluster namespace: %s", val)

	if val == "" {
		log.L.WithName("sTeam.ListRelatedClusterNamespace").Warningf(ctx, "team related cluster namespace is empty")
		return res, nil
	}

	err = json.Unmarshal([]byte(val), &res)
	if err != nil {
		log.L.WithName("sTeam.ListRelatedClusterNamespace").Errorf(ctx, "unmarshal team"+
			" related cluster namespace error:%s", err.Error())
		return res, err
	}

	return
}

func (this *sTeam) ListTeamUser(ctx context.Context, teamId int) (res []*dto.UserTeamRole, err error) {
	res = make([]*dto.UserTeamRole, 0)

	// 首先根据teamId查询团队信息，获取业务teamId
	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(dao.Team.Columns().Id, teamId).Scan(teamEntity)
	if err != nil {
		return nil, fmt.Errorf("查询团队信息失败: %s", err.Error())
	}

	// 使用原生SQL查询用户、角色、团队关系
	db, err := gdb.Instance()
	if err != nil {
		return nil, err
	}

	sql := `
SELECT
  u.id AS user_id,
  u.username,
  u.nick_name,
  u.email,
  u.employee_no,
  r.name AS role_name,
  t.team_id,
  t.category
FROM
  tt_user u
  INNER JOIN tt_user_role_team_rela urt ON u.id = urt.user_id
  INNER JOIN tt_team t ON urt.team_id = t.team_id
  INNER JOIN tt_role r ON urt.role_id = r.id
WHERE t.team_id = ?
`

	type queryResult struct {
		UserId     int    `json:"user_id"`
		Username   string `json:"username"`
		NickName   string `json:"nick_name"`
		Email      string `json:"email"`
		EmployeeNo string `json:"employee_no"`
		RoleName   string `json:"role_name"`
		TeamId     string `json:"team_id"`
		Category   string `json:"category"`
	}

	results := make([]*queryResult, 0)
	all, err := db.Ctx(ctx).GetAll(ctx, sql, teamEntity.TeamId)
	if err != nil {
		return nil, fmt.Errorf("查询团队用户失败: %s", err.Error())
	}

	err = all.Structs(&results)
	if err != nil {
		return nil, fmt.Errorf("解析查询结果失败: %s", err.Error())
	}

	// 转换查询结果为返回格式
	for _, result := range results {
		var role auth.TeamRoleKind
		switch result.RoleName {
		case string(auth.RoleTeamAdmin):
			role = auth.TeamRoleAdmin
		case string(auth.RoleUser):
			role = auth.TeamRoleUser
		default:
			// 跳过不认识的角色
			continue
		}

		res = append(res, &dto.UserTeamRole{
			TeamId:     result.TeamId,
			UserId:     result.UserId,
			Role:       role,
			Username:   result.Username,
			NickName:   result.NickName,
			Email:      result.Email,
			EmployeeNo: result.EmployeeNo,
			Id:         teamId,
			TeamName:   teamEntity.Name,
			TeamType:   auth.TeamKind(teamEntity.Category),
		})
	}

	return
}

type teamSynchronizer struct {
}

func newTeamSynchronizer() *teamSynchronizer {
	return &teamSynchronizer{}
}

func (this *teamSynchronizer) start(ctx context.Context) (err error) {
	projects, err := client.CicdAppProject.ListProject(ctx)
	if err != nil {
		return err
	}

	teamEntities := make([]*entity.Team, 0, len(projects))

	for _, project := range projects {
		teamEntities = append(teamEntities, &entity.Team{
			Category: string(auth.TeamFeature),
			Name:     project.Name,
			TeamId:   fmt.Sprintf("%d", project.Id),
		})
	}

	// 直接用cicd 团队id存储即可
	_, err = dao.Team.Ctx(ctx).Data(teamEntities).FieldsEx(dao.Team.Columns().Id).Batch(50).Save()
	if err != nil {
		return err
	}

	return
}
