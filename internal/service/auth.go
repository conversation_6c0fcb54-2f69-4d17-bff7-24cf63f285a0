// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	authConsts "mlops/internal/consts/auth"
	"mlops/internal/model/dto"

	jwt "github.com/gogf/gf-jwt/v2"
)

type (
	IJwtAuth interface {
		Do() *jwt.GfJWTMiddleware
	}
	IPlatFormAuth interface {
		// ApiRegister api 注册
		ApiRegister(ctx context.Context, apiReq ...interface{})
		ShowApiAuthList(ctx context.Context)
		GlobalCheck(ctx context.Context, userId int, uri string) bool
		CheckAuthWithoutTeam(ctx context.Context, userId int, roles []string) bool
		CheckTeamAuth(ctx context.Context, userId int, teamId int, roles []string) bool
		BindAdmin(ctx context.Context, uid int) (err error)
		UnBindAdmin(ctx context.Context, uid int) (err error)
		BindUser(ctx context.Context, uid int) (err error)
		BindKnowledgeBaseAdmin(ctx context.Context, uid int) (err error)
		UnBindKnowledgeBaseAdmin(ctx context.Context, uid int) (err error)
		BindConsoleAdmin(ctx context.Context, uid int) (err error)
		UnBindConsoleAdmin(ctx context.Context, uid int) (err error)
		// BindTeamRole 绑定团队角色, 相当于替换权限
		BindTeamRole(ctx context.Context, uid int, teamId int, kind authConsts.TeamRoleKind) (err error)
		UnBindTeamRole(ctx context.Context, uid int, teamId int, kind authConsts.TeamRoleKind) (err error)
		GetUserAuthProfile(ctx context.Context, uid int) (authProfile *dto.AuthProfile, err error)
		SyncUserTeam(ctx context.Context, uid int) (err error)
		CheckAdmin(ctx context.Context, userId int) bool
	}
)

var (
	localJwtAuth      IJwtAuth
	localPlatFormAuth IPlatFormAuth
)

func JwtAuth() IJwtAuth {
	if localJwtAuth == nil {
		panic("implement not found for interface IJwtAuth, forgot register?")
	}
	return localJwtAuth
}

func RegisterJwtAuth(i IJwtAuth) {
	localJwtAuth = i
}

func PlatFormAuth() IPlatFormAuth {
	if localPlatFormAuth == nil {
		panic("implement not found for interface IPlatFormAuth, forgot register?")
	}
	return localPlatFormAuth
}

func RegisterPlatFormAuth(i IPlatFormAuth) {
	localPlatFormAuth = i
}
