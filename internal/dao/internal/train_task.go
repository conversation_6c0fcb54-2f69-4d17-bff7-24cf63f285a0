// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TrainTaskDao is the data access object for the table tt_train_task.
type TrainTaskDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TrainTaskColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TrainTaskColumns defines and stores column names for the table tt_train_task.
type TrainTaskColumns struct {
	Id                  string // 主键
	TeamId              string // 团队ID
	TeamName            string // 组名
	TaskName            string // 任务名称
	ClusterName         string // 集群名称
	ClusterId           string // 集群ID
	Namespace           string // 命名空间
	ImageUrl            string // 镜像地址
	EnvVars             string // 环境变量
	StartCmd            string // 启动命令
	Priority            string // 优先级
	TaskType            string // 任务类型
	TaskYaml            string // task yaml
	TriggerCount        string // 触发次数
	CompleteCount       string // 完成次数
	LastStatus          string // 最近一次执行状态
	CreatedByUserName   string // 创建用户名
	CreatedByEmployeeNo string // 创建用户工号
	UpdatedByUserName   string // 最后更新用户名
	UpdatedByEmployeeNo string // 最后更新用户工号
	CreatedAt           string // 创建时间
	UpdatedAt           string // 更新时间
	DeletedAt           string // 软删除时间（NULL表示未删除）
}

// trainTaskColumns holds the columns for the table tt_train_task.
var trainTaskColumns = TrainTaskColumns{
	Id:                  "id",
	TeamId:              "team_id",
	TeamName:            "team_name",
	TaskName:            "task_name",
	ClusterName:         "cluster_name",
	ClusterId:           "cluster_id",
	Namespace:           "namespace",
	ImageUrl:            "image_url",
	EnvVars:             "env_vars",
	StartCmd:            "start_cmd",
	Priority:            "priority",
	TaskType:            "task_type",
	TaskYaml:            "task_yaml",
	TriggerCount:        "trigger_count",
	CompleteCount:       "complete_count",
	LastStatus:          "last_status",
	CreatedByUserName:   "created_by_user_name",
	CreatedByEmployeeNo: "created_by_employee_no",
	UpdatedByUserName:   "updated_by_user_name",
	UpdatedByEmployeeNo: "updated_by_employee_no",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	DeletedAt:           "deleted_at",
}

// NewTrainTaskDao creates and returns a new DAO object for table data access.
func NewTrainTaskDao(handlers ...gdb.ModelHandler) *TrainTaskDao {
	return &TrainTaskDao{
		group:    "default",
		table:    "tt_train_task",
		columns:  trainTaskColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TrainTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TrainTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TrainTaskDao) Columns() TrainTaskColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TrainTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TrainTaskDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TrainTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
