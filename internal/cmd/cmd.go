package cmd

import (
	"context"
	"mlops/api"
	"mlops/internal/consts"
	"mlops/internal/controller/jwtauth"
	"mlops/internal/controller/onlinedevelopment"
	"mlops/internal/controller/setting"
	"mlops/internal/controller/sso"
	"mlops/internal/controller/team"
	"mlops/internal/controller/traintask"
	"mlops/internal/controller/traintaskexecution"
	"mlops/internal/controller/user"
	"mlops/internal/service"
	"time"

	"github.com/gogf/gf/v2/util/gmode"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/constackinit"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	"mlops/internal/controller/hello"
)

const (
	APIV1 = "/api/v1"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server(consts.ServerName)
			s.EnablePProf()

			constackinit.Initialize()

			// HOOK, 开发阶段禁止浏览器缓存,方便调试
			if gmode.IsDevelop() {
				s.BindHookHandler("/*", ghttp.HookBeforeServe, func(r *ghttp.Request) {
					r.Response.Header().Set("Cache-Control", "no-store")
				})
			}
			s.BindHookHandler("/*", ghttp.HookBeforeServe, func(r *ghttp.Request) {
				r.Response.CORSDefault()
			})

			// healthy-check
			s.BindHandler("/healthy-check", func(r *ghttp.Request) {
				r.Response.Write("ok")
			})

			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(
					// request
					// response
					service.Middleware().ErrorRespHandler,
					service.Middleware().MiddlewareHandlerResponse,
				)
				group.Bind(
					hello.NewV1(),
					sso.NewV1(),
				)

			})

			s.Group(APIV1, func(group *ghttp.RouterGroup) {
				group.Middleware(
					// request
					// response
					service.Middleware().ErrorRespHandler,
					service.Middleware().MiddlewareHandlerResponse,
				)
				group.Bind(
					jwtauth.NewV1(),
				)

				group.Group("/", func(group *ghttp.RouterGroup) {
					// request
					group.Middleware(service.Middleware().JwtAuth)
					group.Middleware(service.Middleware().CheckUserIsActive)
					// response

					group.Bind(
						setting.NewV1(),
						user.NewV1(),
						traintask.NewV1(),
						traintaskexecution.NewV1(),
						team.NewV1(),
						onlinedevelopment.NewV1(),
					)
				})
			})

			PlatformAuthApiRegister(ctx)

			go RunCronJob(ctx)

			s.Run()
			return nil
		},
	}
)

func RunCronJob(ctx context.Context) {
	// sleep  延迟启动10秒
	time.Sleep(10 * time.Second)

	// test
	if err := service.CronJob().SyncCicdTeam(ctx); err != nil {
		log.L.WithName("RunCronJob").Errorf(ctx, "sync cicd team error:%s", err.Error())
	}

	if err := service.CronJob().SyncTrainTaskExecution(ctx); err != nil {
		log.L.WithName("RunCronJob").Errorf(ctx, "sync train task execution error:%s", err.Error())
	}

	if err := service.CronJob().SyncOnlineDevelopmentStatus(ctx); err != nil {
		log.L.WithName("RunCronJob").Errorf(ctx, "sync online development status error:%s", err.Error())
	}
}

func PlatformAuthApiRegister(ctx context.Context) {
	api.ApiRegister(ctx)
	service.PlatFormAuth().ShowApiAuthList(ctx)
}
