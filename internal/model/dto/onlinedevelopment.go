package dto

import "github.com/gogf/gf/v2/os/gtime"

type OnlineDevelopmentListInput struct {
	Page                int `v:"required"`
	PageSize            int `v:"required"`
	DevName             string
	TeamId              int
	CreatedByUserName   string
	CreatedByEmployeeNo string
	UpdatedByUserName   string
	UpdatedByEmployeeNo string
	CreatedAt           []*gtime.Time
	UpdatedAt           []*gtime.Time
	OnlyMy              bool
	Pid                 uint `json:"pid"`
}

type OnlineDevelopmentListOutput struct {
	List        []*OnlineDevelopment `json:"list"`
	Total       int                  `json:"total"`
	CurrentPage int                  `json:"currentPage"`
	PageSize    int                  `json:"pageSize"`
}
type OnlineDevelopment struct {
	Id                  uint                             `json:"id"`
	DevName             string                           `json:"devName"`
	TeamId              int                              `json:"teamId"`
	TeamName            string                           `json:"teamName"`
	ClusterName         string                           `json:"clusterName"`
	ClusterId           uint                             `json:"clusterId"`
	Namespace           string                           `json:"namespace"`
	ImageUrl            string                           `json:"imageUrl"`
	DevUrl              string                           `json:"devUrl"`
	Type                string                           `json:"type"`
	Status              string                           `json:"status"`
	CreatedByUserName   string                           `json:"createdByUserName"`
	CreatedByEmployeeNo string                           `json:"createdByEmployeeNo"`
	UpdatedByUserName   string                           `json:"updatedByUserName"`
	UpdatedByEmployeeNo string                           `json:"updatedByEmployeeNo"`
	CreatedAt           *gtime.Time                      `json:"createdAt"`
	UpdatedAt           *gtime.Time                      `json:"updatedAt"`
	ClusterResource     OnlineDevelopmentClusterResource `json:"clusterResource"`
	VolumeMounts        []*OnlineDevelopmentVolumeMount  `json:"volumeMounts"`
	Password            string                           `json:"password"`
	Env                 map[string]string                `json:"envVars"`
}

type OnlineDevelopmentClusterResource struct {
	DevId            uint   `json:"devId"`
	GpuType          string `json:"gpuType"`
	RequestCpu       string `json:"requestCpu"`
	RequestMemory    string `json:"requestMemory"`
	RequestGpuCore   string `json:"requestGpuCore"`
	RequestGpuMemory string `json:"requestGpuMemory"`
	LimitCpu         string `json:"limitCpu"`
	LimitMemory      string `json:"limitMemory"`
	LimitGpuCore     string `json:"limitGpuCore"`
	LimitGpuMemory   string `json:"limitGpuMemory"`
}

type OnlineDevelopmentVolumeMount struct {
	Name       string `json:"name"`
	DevId      uint   `json:"devId"`
	VolumeName string `json:"volumeName"`
	MountPath  string `json:"mountPath"`
	SubPath    string `json:"subPath"`
	VolumeType string `json:"volumeType"`
}
