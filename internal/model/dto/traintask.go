package dto

import "github.com/gogf/gf/v2/os/gtime"

type TrainTaskListInput struct {
	Page                int `v:"required"`
	PageSize            int `v:"required"`
	TaskName            string
	TeamId              int
	Statuses            []string
	CreatedByUserName   string
	CreatedByEmployeeNo string
	UpdatedByUserName   string
	UpdatedByEmployeeNo string
	CreatedAt           []*gtime.Time
	UpdatedAt           []*gtime.Time
	OnlyMy              bool
	OnlyUnfinished      bool
}

type TrainTaskListOutput struct {
	List        []*TrainTask `json:"list"`
	Total       int          `json:"total"`
	CurrentPage int          `json:"currentPage"`
	PageSize    int          `json:"pageSize"`
}

type TrainTask struct {
	Id                  uint                     `json:"id"`
	TaskName            string                   `json:"taskName"`
	TeamId              uint                     `json:"teamId"`
	TeamName            string                   `json:"teamName"`
	ClusterName         string                   `json:"clusterName"`
	ClusterId           uint                     `json:"clusterId"`
	Namespace           string                   `json:"namespace"`
	ImageUrl            string                   `json:"imageUrl"`
	EnvVars             string                   `json:"-"` // 这个字段json不返回
	StartCmd            string                   `json:"startCmd"`
	Priority            string                   `json:"priority"`
	TaskType            string                   `json:"taskType"`
	TaskYaml            string                   `json:"taskYaml"`
	LastStatus          string                   `json:"lastStatus"`
	CreatedByUserName   string                   `json:"createdByUserName"`
	CreatedByEmployeeNo string                   `json:"createdByEmployeeNo"`
	UpdatedByUserName   string                   `json:"updatedByUserName"`
	UpdatedByEmployeeNo string                   `json:"updatedByEmployeeNo"`
	TriggerCount        uint                     `json:"triggerCount"`
	CompleteCount       uint                     `json:"completeCount"`
	CreatedAt           *gtime.Time              `json:"createdAt"`
	UpdatedAt           *gtime.Time              `json:"updatedAt"`
	ClusterResource     TrainTaskClusterResource `json:"clusterResource"`
	VolumeMounts        []*TrainTaskVolumeMount  `json:"volumeMounts"`
	EnvVarsMap          map[string]string        `json:"envVars"`
}

type TrainTaskClusterResource struct {
	TaskId           uint   `json:"taskId"`
	MinReplicas      uint   `json:"minReplicas"`
	MaxReplicas      uint   `json:"maxReplicas"`
	GpuType          string `json:"gpuType"`
	RequestCpu       string `json:"requestCpu"`
	RequestMemory    string `json:"requestMemory"`
	RequestGpuCore   string `json:"requestGpuCore"`
	RequestGpuMemory string `json:"requestGpuMemory"`
	LimitCpu         string `json:"limitCpu"`
	LimitMemory      string `json:"limitMemory"`
	LimitGpuCore     string `json:"limitGpuCore"`
	LimitGpuMemory   string `json:"limitGpuMemory"`
}

type TrainTaskVolumeMount struct {
	Name       string `json:"name"`
	TaskId     uint   `json:"taskId"`
	VolumeName string `json:"volumeName"`
	MountPath  string `json:"mountPath"`
	SubPath    string `json:"subPath"`
	VolumeType string `json:"volumeType"`
}
