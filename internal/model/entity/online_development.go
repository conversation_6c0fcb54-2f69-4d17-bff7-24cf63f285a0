// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// OnlineDevelopment is the golang structure for table online_development.
type OnlineDevelopment struct {
	Id                  uint        `json:"id"                  orm:"id"                     description:"主键（自增ID）"`
	DevName             string      `json:"devName"             orm:"dev_name"               description:"开发环境名称"`
	ClusterName         string      `json:"clusterName"         orm:"cluster_name"           description:"集群名称"`
	ClusterId           uint        `json:"clusterId"           orm:"cluster_id"             description:"集群ID"`
	Namespace           string      `json:"namespace"           orm:"namespace"              description:"命名空间"`
	ImageUrl            string      `json:"imageUrl"            orm:"image_url"              description:"镜像地址"`
	DevUrl              string      `json:"devUrl"              orm:"dev_url"                description:"开发环境URL（唯一访问地址）"`
	CreatedByUserName   string      `json:"createdByUserName"   orm:"created_by_user_name"   description:"创建人"`
	CreatedByEmployeeNo string      `json:"createdByEmployeeNo" orm:"created_by_employee_no" description:""`
	UpdatedByUserName   string      `json:"updatedByUserName"   orm:"updated_by_user_name"   description:"最后更新人"`
	UpdatedByEmployeeNo string      `json:"updatedByEmployeeNo" orm:"updated_by_employee_no" description:""`
	CreatedAt           *gtime.Time `json:"createdAt"           orm:"created_at"             description:"创建时间"`
	UpdatedAt           *gtime.Time `json:"updatedAt"           orm:"updated_at"             description:"更新时间"`
	DeletedAt           *gtime.Time `json:"deletedAt"           orm:"deleted_at"             description:"软删除时间（NULL表示未删除）"`
	TeamId              int         `json:"teamId"              orm:"team_id"                description:"团队id"`
	TeamName            string      `json:"teamName"            orm:"team_name"              description:"团队名"`
	Type                string      `json:"type"                orm:"type"                   description:"开发环境类型"`
	Password            string      `json:"password"            orm:"password"               description:""`
	Env                 string      `json:"env"                 orm:"env"                    description:""`
	Status              string      `json:"status"              orm:"status"                 description:""`
}
