// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTask is the golang structure for table train_task.
type TrainTask struct {
	Id                  uint        `json:"id"                  orm:"id"                     description:"主键"`
	TeamId              uint        `json:"teamId"              orm:"team_id"                description:"团队ID"`
	TeamName            string      `json:"teamName"            orm:"team_name"              description:"组名"`
	TaskName            string      `json:"taskName"            orm:"task_name"              description:"任务名称"`
	ClusterName         string      `json:"clusterName"         orm:"cluster_name"           description:"集群名称"`
	ClusterId           uint        `json:"clusterId"           orm:"cluster_id"             description:"集群ID"`
	Namespace           string      `json:"namespace"           orm:"namespace"              description:"命名空间"`
	ImageUrl            string      `json:"imageUrl"            orm:"image_url"              description:"镜像地址"`
	EnvVars             string      `json:"envVars"             orm:"env_vars"               description:"环境变量"`
	StartCmd            string      `json:"startCmd"            orm:"start_cmd"              description:"启动命令"`
	Priority            string      `json:"priority"            orm:"priority"               description:"优先级"`
	TaskType            string      `json:"taskType"            orm:"task_type"              description:"任务类型"`
	TaskYaml            string      `json:"taskYaml"            orm:"task_yaml"              description:"task yaml"`
	TriggerCount        uint        `json:"triggerCount"        orm:"trigger_count"          description:"触发次数"`
	CompleteCount       uint        `json:"completeCount"       orm:"complete_count"         description:"完成次数"`
	LastStatus          string      `json:"lastStatus"          orm:"last_status"            description:"最近一次执行状态"`
	CreatedByUserName   string      `json:"createdByUserName"   orm:"created_by_user_name"   description:"创建用户名"`
	CreatedByEmployeeNo string      `json:"createdByEmployeeNo" orm:"created_by_employee_no" description:"创建用户工号"`
	UpdatedByUserName   string      `json:"updatedByUserName"   orm:"updated_by_user_name"   description:"最后更新用户名"`
	UpdatedByEmployeeNo string      `json:"updatedByEmployeeNo" orm:"updated_by_employee_no" description:"最后更新用户工号"`
	CreatedAt           *gtime.Time `json:"createdAt"           orm:"created_at"             description:"创建时间"`
	UpdatedAt           *gtime.Time `json:"updatedAt"           orm:"updated_at"             description:"更新时间"`
	DeletedAt           *gtime.Time `json:"deletedAt"           orm:"deleted_at"             description:"软删除时间（NULL表示未删除）"`
}
