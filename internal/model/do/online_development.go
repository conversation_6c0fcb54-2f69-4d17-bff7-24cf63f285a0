// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// OnlineDevelopment is the golang structure of table tt_online_development for DAO operations like Where/Data.
type OnlineDevelopment struct {
	g.Meta              `orm:"table:tt_online_development, do:true"`
	Id                  interface{} // 主键（自增ID）
	DevName             interface{} // 开发环境名称
	ClusterName         interface{} // 集群名称
	ClusterId           interface{} // 集群ID
	Namespace           interface{} // 命名空间
	ImageUrl            interface{} // 镜像地址
	DevUrl              interface{} // 开发环境URL（唯一访问地址）
	CreatedByUserName   interface{} // 创建人
	CreatedByEmployeeNo interface{} //
	UpdatedByUserName   interface{} // 最后更新人
	UpdatedByEmployeeNo interface{} //
	CreatedAt           *gtime.Time // 创建时间
	UpdatedAt           *gtime.Time // 更新时间
	DeletedAt           *gtime.Time // 软删除时间（NULL表示未删除）
	TeamId              interface{} // 团队id
	TeamName            interface{} // 团队名
	Type                interface{} // 开发环境类型
	Password            interface{} //
	Env                 interface{} //
	Status              interface{} //
}
