package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/consts"
	"mlops/internal/model/dto"
)

type ListOnlineDevelopmentReq struct {
	g.Meta `path:"/online-development/list" method:"get" tags:"Online Development" sm:"list online development"`
	dto.OnlineDevelopmentListInput
}

type ListOnlineDevelopmentRes struct {
	dto.OnlineDevelopmentListOutput
}

type GetOnlineDevelopmentReq struct {
	g.Meta `path:"/online-development/:id" method:"get" tags:"Online Development" sm:"get online development"`
}

type GetOnlineDevelopmentRes struct {
	Data interface{} `json:"data"`
}

type CreateOnlineDevelopmentReq struct {
	g.Meta              `path:"/online-development/create" method:"post" tags:"Online Development" sm:"create online development"`
	TeamId              int                                  `json:"teamId"`
	TeamName            string                               `json:"teamName"`
	ClusterName         string                               `json:"clusterName"`
	ClusterId           uint                                 `json:"clusterId"`
	Namespace           string                               `json:"namespace"`
	DevName             string                               `json:"devName"`
	ImageUrl            string                               `json:"imageUrl"`
	Type                string                               `json:"type"`
	CreatedByEmployeeNo string                               `json:"createdByEmployeeNo"`
	CreatedByUserName   string                               `json:"createdByUserName"`
	UpdatedByEmployeeNo string                               `json:"updatedByEmployeeNo"`
	UpdatedByUserName   string                               `json:"updatedByUserName"`
	ClusterResource     dto.OnlineDevelopmentClusterResource `json:"clusterResource"`
	VolumeMounts        []*dto.OnlineDevelopmentVolumeMount  `json:"volumeMounts"`
	Env                 map[string]string                    `json:"envVars"`
}

type CreateOnlineDevelopmentRes struct {
}

type UpdateOnlineDevelopmentReq struct {
	g.Meta              `path:"/online-development/update/:id" method:"patch" tags:"Online Development" sm:"update online development"`
	DevName             string                               `json:"devName"`
	ClusterName         string                               `json:"clusterName"`
	ClusterId           uint                                 `json:"clusterId"`
	Namespace           string                               `json:"namespace"`
	ImageUrl            string                               `json:"imageUrl"`
	Type                string                               `json:"type"`
	CreatedByEmployeeNo string                               `json:"createdByEmployeeNo"`
	CreatedByUserName   string                               `json:"createdByUserName"`
	UpdatedByEmployeeNo string                               `json:"updatedByEmployeeNo"`
	UpdatedByUserName   string                               `json:"updatedByUserName"`
	ClusterResource     dto.OnlineDevelopmentClusterResource `json:"clusterResource"`
	VolumeMounts        []*dto.OnlineDevelopmentVolumeMount  `json:"volumeMounts"`
	Env                 map[string]string                    `json:"envVars"`
}

type UpdateOnlineDevelopmentRes struct {
}

type DeleteOnlineDevelopmentReq struct {
	g.Meta `path:"/online-development/delete/:id" method:"delete" tags:"Online Development" sm:"delete online development"`
}

type DeleteOnlineDevelopmentRes struct {
}

type StartOnlineDevelopmentReq struct {
	g.Meta `path:"/online-development/start/:id" method:"post" tags:"Online Development" sm:"start online development"`
}

type StartOnlineDevelopmentRes struct {
}

type StopOnlineDevelopmentReq struct {
	g.Meta `path:"/online-development/stop/:id" method:"post" tags:"Online Development" sm:"stop online development"`
}

type StopOnlineDevelopmentRes struct {
}

type ListOnlineDevelopmentImageReq struct {
	g.Meta `path:"/online-development/image/list" method:"get" tags:"Online Development" sm:"list image"`
	Type   consts.OnlineDevType `json:"type"`
}

type ListOnlineDevelopmentImageRes struct {
	Images []string `json:"images"`
}
