package client

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	v1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/config"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	appsv1 "k8s.io/api/apps/v1"
	core_v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

var (
	ConstackResourceAny *constack_openapi.ResourceAny
)

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.constack.authorization")
	host, _ := g.Cfg().Get(context.Background(), "api.constack.host")
	ConstackResourceAny = NewConstackResourceAnyClient(context.Background(), token.String(), host.String())
}

func NewConstackResourceAnyClient(ctx context.Context, token, host string) *constack_openapi.ResourceAny {
	cfg := config.NewConfig(ctx, token)

	cfg.CurrenBaseOn = config.BaseOnK8s
	cfg.Host = host

	ctrl := resources.NewResourceController[*constack_openapi.ResourceAny, constack_openapi.ResourceAny](cfg)

	return ctrl
}

func convert(object runtime.Object) (map[string]interface{}, error) {
	m := make(map[string]interface{})
	marshal, _ := json.Marshal(object)
	err := json.Unmarshal(marshal, &m)

	return m, err
}

func CreateRayJob(ctx context.Context, clusterName string, rayjob *v1.RayJob) (err error) {
	m, err := convert(rayjob)
	if err != nil {
		log.L.WithName("CreateRayJob").Errorf(ctx, "convert rayjob error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateRayJob").Errorf(ctx, "create rayjob error:%s", err.Error())
		return err
	}

	return nil
}

func DeleteRayJob(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		v1.GroupVersion.Group, v1.GroupVersion.Version, "rayjobs", name)
	if err != nil {
		log.L.WithName("DeleteRayJob").Errorf(ctx, "delete rayjob error:%s", err.Error())
		return err
	}

	return nil
}
func GetRayJob(ctx context.Context, clusterName, namespace, name string) (rayjob *v1.RayJob, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		v1.GroupVersion.Group, v1.GroupVersion.Version, "rayjobs", name)
	if err != nil {
		return nil, err
	}

	rayjobs := []*v1.RayJob{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &rayjobs)
	if err != nil {
		log.L.WithName("GetRayJob").Errorf(ctx, "unmarshal rayjob error:%s", err.Error())
		return nil, err
	}
	if len(rayjobs) > 0 {
		return rayjobs[0], nil
	}
	return nil, fmt.Errorf("not found rayjob:%s", name)
}

func ListConfigmap(ctx context.Context, clusterName, namespace string) (configmaps []core_v1.ConfigMap, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		core_v1.SchemeGroupVersion.Group, core_v1.SchemeGroupVersion.Version,
		"configmaps", []constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListConfigmap").Errorf(ctx, "ListV2WithClusterName error:%s", err.Error())
		return nil, err
	}

	configmaps = make([]core_v1.ConfigMap, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &configmaps)
	if err != nil {
		log.L.WithName("ListConfigmap").Errorf(ctx, "unmarshal configmap error:%s", err.Error())
		return nil, err
	}

	return
}

func ListSecret(ctx context.Context, clusterName, namespace string) (secrets []core_v1.Secret, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		core_v1.SchemeGroupVersion.Group, core_v1.SchemeGroupVersion.Version,
		"secrets", []constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListSecret").Errorf(ctx, "ListV2WithClusterName error:%s", err.Error())
		return nil, err
	}

	secrets = make([]core_v1.Secret, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &secrets)
	if err != nil {
		log.L.WithName("ListSecret").Errorf(ctx, "unmarshal secret error:%s", err.Error())
		return nil, err
	}

	return
}

func ListPvc(ctx context.Context, clusterName, namespace string) (pvcs []core_v1.PersistentVolumeClaim, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		core_v1.SchemeGroupVersion.Group, core_v1.SchemeGroupVersion.Version,
		"persistentvolumeclaims", []constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListPvc").Errorf(ctx, "ListV2WithClusterName error:%s", err.Error())
		return nil, err
	}

	pvcs = make([]core_v1.PersistentVolumeClaim, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &pvcs)
	if err != nil {
		log.L.WithName("ListPvc").Errorf(ctx, "unmarshal pvc error:%s", err.Error())
		return nil, err
	}

	return
}

func CreateDeployment(ctx context.Context, clusterName string, deployment *appsv1.Deployment) (err error) {
	m, err := convert(deployment)
	if err != nil {
		log.L.WithName("CreateDeployment").Errorf(ctx, "convert deployment error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateDeployment").Errorf(ctx, "create deployment error:%s", err.Error())
		return err
	}

	return nil
}

func DeleteDeployment(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		appsv1.SchemeGroupVersion.Group, appsv1.SchemeGroupVersion.Version, "deployments", name)
	if err != nil {
		log.L.WithName("DeleteDeployment").Errorf(ctx, "delete deployment error:%s", err.Error())
		return err
	}

	return nil
}

func GetDeployment(ctx context.Context, clusterName, namespace, name string) (deployment *appsv1.Deployment, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		appsv1.SchemeGroupVersion.Group, appsv1.SchemeGroupVersion.Version, "deployments", name)
	if err != nil {
		return nil, err
	}

	deployments := []*appsv1.Deployment{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &deployments)
	if err != nil {
		log.L.WithName("GetDeployment").Errorf(ctx, "unmarshal deployment error:%s", err.Error())
		return nil, err
	}
	if len(deployments) > 0 {
		return deployments[0], nil
	}
	return nil, fmt.Errorf("not found deployment:%s", name)
}

func CreateService(ctx context.Context, clusterName string, service *core_v1.Service) (err error) {
	m, err := convert(service)
	if err != nil {
		log.L.WithName("CreateService").Errorf(ctx, "convert service error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateService").Errorf(ctx, "create service error:%s", err.Error())
		return err
	}

	return nil
}

func DeleteService(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		core_v1.SchemeGroupVersion.Group, core_v1.SchemeGroupVersion.Version, "services", name)
	if err != nil {
		log.L.WithName("DeleteService").Errorf(ctx, "delete service error:%s", err.Error())
		return err
	}

	return nil
}

func GetService(ctx context.Context, clusterName, namespace, name string) (service *core_v1.Service, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		core_v1.SchemeGroupVersion.Group, core_v1.SchemeGroupVersion.Version, "services", name)
	if err != nil {
		return nil, err
	}

	services := []*core_v1.Service{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &services)
	if err != nil {
		log.L.WithName("GetService").Errorf(ctx, "unmarshal service error:%s", err.Error())
		return nil, err
	}
	if len(services) > 0 {
		return services[0], nil
	}
	return nil, fmt.Errorf("not found service:%s", name)
}
