package client

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/tools/client/httpclient"
	"strings"
)

var _ Service = (*Client)(nil)

const (
	CMDB_APPID_EXISTED = -101
	ModelList          = "/cmdb/inner-general-model/v2/%v/"
)

type Client struct {
	Host    string
	Token   string
	session httpclient.Session
}

func NewClient(host, token string) *Client {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": token,
	}
	session.SetHeaders(headers)
	client := &Client{
		Host:    host,
		Token:   token,
		session: session,
	}
	return client
}

// Service _
type Service interface {
	// PageJumpPodList 跳转牵星pod列表
	PageJumpPodList(context.Context, *PageJumpPodListReq) (*PageJumpPodListResp, error)
}

func (c *Client) buildUrl(path string) string {
	newUrl := strings.Replace(c.Host, "/api/jsonrpc/", "", 1) + fmt.Sprintf(path)
	return newUrl
}

type PageJumpPodListReq struct {
	ClusterName string `json:"clusterName"`
	Namespace   string `json:"namespace"`
	PodName     string `json:"podName"`
	Username    string `json:"username"`
}

type PageJumpPodListResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Url string `json:"url"`
	} `json:"data"`
}

func (c *Client) PageJumpPodList(ctx context.Context, req *PageJumpPodListReq) (*PageJumpPodListResp, error) {
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "new request error:%s", err.Error())
		return nil, err
	}
	httpResp, err := c.session.Get(context.Background(), httpReq)
	if err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "get response error:%s", err.Error())
		return nil, err
	}
	var resp PageJumpPodListResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "unmarshal response error:%s", err.Error())
		return nil, err
	}
	return &resp, nil
}
