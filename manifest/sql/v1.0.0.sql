CREATE DATABASE `mlops` /*!40100 DEFAULT CHARACTER SET utf8mb4 */

create table tt_openapi_token
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(128) null comment '唯一标识',
    token      varchar(256) null comment 'token',
    `desc`     varchar(128) null comment '描述',
    created_at datetime     null,
    updated_at datetime     null,
    deleted_at datetime     null,
    constraint idx_uniq_name
        unique (name)
);

create table tt_role
(
    id         bigint auto_increment
        primary key,
    name       varchar(64)  null,
    `desc`     varchar(512) null,
    created_at datetime     null,
    updated_at datetime     null,
    deleted_at datetime     null
);





create table tt_setting
(
    id         int(11) unsigned auto_increment
        primary key,
    `key`      varchar(128) null,
    value      text         null,
    category   varchar(64)  null,
    `desc`     varchar(128) null,
    created_at datetime     null,
    updated_at datetime     null,
    deleted_at datetime     null,
    constraint idx_uniq_idx
        unique (`key`)
)
    row_format = DYNAMIC;

create index idx_deleted_at
    on tt_setting (deleted_at);

create table tt_team
(
    id         bigint auto_increment
        primary key,
    category   varchar(64)  null comment '分类',
    name       varchar(128) null comment '团队名',
    team_id    varchar(64)  null comment '团队业务id',
    created_at datetime     null,
    updated_at datetime     null,
    deleted_at datetime     null,
    constraint uniq_teamid
        unique (team_id)
);

create table tt_train_task
(
    id                     int unsigned auto_increment comment '主键'
        primary key,
    team_id                int unsigned                                            not null comment '团队ID',
    team_name              varchar(255)                                            not null comment '组名',
    task_name              varchar(255)                                            not null comment '任务名称',
    cluster_name           varchar(255)                                            not null comment '集群名称',
    cluster_id             int unsigned                                            not null comment '集群ID',
    namespace              varchar(100)                                            not null comment '命名空间',
    image_url              varchar(500)                                            not null comment '镜像地址',
    start_cmd              varchar(1000)                                           not null comment '启动命令',
    priority               enum ('P0', 'P1', 'P2', 'P3') default 'P1'              not null comment '优先级',
    task_type              enum ('FORM', 'YAML')         default 'FORM'            not null comment '任务类型',
    task_yaml              json                                                    not null comment 'task yaml',
    trigger_count          int unsigned                                            not null comment '触发次数',
    complete_count         int unsigned                                            not null comment '完成次数',
    last_status            varchar(20)                                             null comment '最近一次执行状态',
    created_by_user_name   varchar(100)                                            not null comment '创建用户名',
    created_by_employee_no varchar(100)                                            null comment '创建用户工号',
    updated_by_user_name   varchar(100)                                            not null comment '最后更新用户名',
    updated_by_employee_no varchar(100)                                            null,
    created_at             datetime                      default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at             datetime                      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_at             datetime                                                null comment '软删除时间（NULL表示未删除）'
)
    comment '训练任务表';

create index idx_created_at
    on tt_train_task (created_at);

create index idx_created_by
    on tt_train_task (created_by_user_name);

create index idx_task_name
    on tt_train_task (task_name);

create index idx_team_id
    on tt_train_task (team_id);

create table tt_train_task_cluster_resource
(
    id                 int unsigned auto_increment comment '资源主键'
        primary key,
    task_id            int unsigned                       not null comment '关联训练任务ID（外键）',
    min_replicas       int unsigned                       not null comment '最小副本数',
    max_replicas       int unsigned                       not null comment '最大副本数',
    gpu_type           varchar(20)                        null comment '显卡类型',
    request_cpu        varchar(20)                        not null comment '请求CPU资源（如"2"表示2核）',
    request_memory     varchar(20)                        not null comment '请求内存（如"4Gi"）',
    request_gpu_core   varchar(20)                        not null comment '请求GPU核心数',
    request_gpu_memory varchar(20)                        not null comment '请求GPU显存',
    limit_cpu          varchar(20)                        not null comment 'CPU限制（如"2000m"）',
    limit_memory       varchar(20)                        not null comment '内存限制（如"8Gi"）',
    limit_gpu_core     varchar(20)                        not null comment 'GPU核心限制',
    limit_gpu_memory   varchar(20)                        not null comment 'GPU显存限制',
    created_at         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_at         datetime                           null comment '软删除时间（NULL表示未删除）'
)
    comment '集群资源配置表';

create index idx_task_id
    on tt_train_task_cluster_resource (task_id);

create table tt_train_task_execution
(
    id                       int unsigned auto_increment comment '主键'
        primary key,
    task_id                  int unsigned                                                                              not null comment '关联训练任务ID（外键）',
    team_id                  int unsigned                                                                              not null comment '团队ID',
    team_name                varchar(255)                                                                              not null comment '组名',
    execution_name           varchar(255)                                                                              not null comment 'rayjob name',
    cluster_name             varchar(255)                                                                              not null comment '集群名称',
    cluster_id               int unsigned                                                                              not null comment '集群ID',
    namespace                varchar(100)                                                                              not null comment '命名空间',
    image_url                varchar(500)                                                                              not null comment '镜像地址',
    trigger_time             datetime                                                                                  null comment '触发时间',
    start_time               datetime                                                                                  not null comment '任务开始时间',
    end_time                 datetime                                                                                  null comment '任务结束时间（NULL表示未结束）',
    duration                 int unsigned                                                                              null comment '持续时长（分钟）',
    dashboard_url            varchar(500)                                                                              null comment '监控面板URL',
    monitor_url              varchar(500)                                                                              null comment '历史监控URL',
    status                   enum ('PENDING', 'RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED') default 'PENDING'         not null comment '任务状态',
    trigger_source           enum ('MANUAL', 'SCHEDULED', 'API_CALL')                                                  not null comment '触发来源',
    triggered_by_user_name   varchar(100)                                                                              not null comment '触发用户',
    triggered_by_employee_no varchar(100)                                                                              not null comment '触发用户工号',
    priority                 enum ('P0', 'P1', 'P2', 'P3')                                   default 'P1'              not null comment '优先级',
    created_at               datetime                                                        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at               datetime                                                        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_at               datetime                                                                                  null comment '软删除时间（NULL表示未删除）'
)
    comment '训练任务执行记录表';

create index idx_status
    on tt_train_task_execution (status);

create index idx_task_id
    on tt_train_task_execution (task_id);

create index idx_trigger_source
    on tt_train_task_execution (trigger_source);

create index idx_triggered_by
    on tt_train_task_execution (triggered_by_user_name);

create table tt_train_task_volume_mount
(
    id          int unsigned auto_increment comment '主键（唯一挂载ID）'
        primary key,
    task_id     int unsigned                       not null comment '关联训练任务ID（外键）',
    name        varchar(100)                       not null comment '挂载名称（如"data-volume"）',
    mount_path  varchar(500)                       not null comment '容器内挂载路径（如"/data"）',
    sub_path    varchar(500)                       null comment '子路径（可选，如"dataset/2023"）',
    volume_type varchar(50)                        not null comment '存储类型（如NFS、HostPath、PVC）',
    volume_name varchar(200)                       not null comment '存储卷名称（如"nfs-data-volume"）',
    created_at  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_at  datetime                           null comment '软删除时间（NULL表示未删除）'
)
    comment '存储卷挂载配置表';

create index idx_task_id
    on tt_train_task_volume_mount (task_id);

create table tt_user
(
    id          bigint(11) auto_increment comment '主键自增id'
        primary key,
    uid         varchar(64)  not null comment '三方系统唯一id',
    username    varchar(64)  not null comment '用户名',
    nick_name   varchar(64)  null comment '昵称',
    email       varchar(64)  null comment '邮箱',
    employee_no varchar(12)  null comment '工号',
    created_at  datetime     null,
    updated_at  datetime     null,
    deleted_at  datetime     null,
    password    varchar(128) null,
    is_active   tinyint      null comment '是否激活'
);

create table tt_user_role_team_rela
(
    id      int auto_increment
        primary key,
    user_id int         null,
    role_id int         null,
    team_id varchar(64) null comment 'team表业务id',
    constraint uniq_idx_uid_rid_tid
        unique (user_id, role_id, team_id) comment '唯一键'
)
    comment '用户角色团队关联表
';

